import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import leftSideBar1 from "../assets/userProfile.png";
import leftSideBar2 from "../assets/strategies.png";
import leftSideBar3 from "../assets/equity.png";
import leftSideBar4 from "../assets/portfolio.png";
import leftSideBar5 from "../assets/masterChild.png";
import rightBarTools from "../assets/rightBarTools.png";
import { useSelector, useDispatch } from "react-redux";
import { setBrokers } from "../store/slices/broker";
import { setStrategies } from "../store/slices/strategy";
import ChatBubbleOutlineIcon from "@mui/icons-material/ChatBubbleOutline";

function LeftNav() {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { brokers } = useSelector((state) => state.brokerReducer);
  const { strategies: data } = useSelector((state) => state.strategyReducer);

  const deleteEmptyRow = () => {
    const mandatoryFields = [ "StrategyLabel", "TradingAccount" ];
    const filteredData = data.filter((row) =>
      mandatoryFields.every((field) => row[ field ] !== "")
    );

    if (data.length !== 1 && filteredData.length !== data.length) {
      dispatch(
        setStrategies({
          strategies: filteredData,
        })
      );
    }

    const requiredFields = [ "userId", "name", "broker", "qrCode", "password" ];
    const filteredBrokers = brokers.filter((row) => {
      if (row.broker === "pseudo_account") {
        return true; // Skip validation for pseudo account
      }
      return requiredFields.every((field) => row[ field ] !== "");
    });

    if (brokers.length && filteredBrokers.length !== brokers.length) {
      dispatch(
        setBrokers({
          brokers: filteredBrokers,
        })
      );
    }
  };

  return (
    <div className="left-sidebar" style={{
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      height: "100%",
      padding: "30px 5px",
    }}>
      <div>

        <ul className="left-links" style={{
          listStyle: "none",
          padding: 0,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "5px"
        }}>
          <li
            onClick={() => {
              navigate("/UserProfiles");
              deleteEmptyRow();
            }}
          >
            <img
              style={{
                height: "50px",
                width: "50px",
                padding: "6px",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                transition: "background-color 0.3s",
                backgroundColor: pathname === "/UserProfiles" ? "#4661bd" : "#d8e1ff",
                cursor: "pointer",
              }}
              src={leftSideBar1}
              alt="User Profiles"
            />
            <span className="tooltip">User Profiles</span>
          </li>

          <li
            onClick={() => {
              navigate("/Strategies");
              deleteEmptyRow();
            }}
          >
            <img
              style={{
                height: "50px",
                width: "50px",
                padding: "6px",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                transition: "background-color 0.3s",
                backgroundColor: pathname === "/Strategies" ? "#4661bd" : "#d8e1ff",
                cursor: "pointer",
              }}
              src={leftSideBar2}
              alt="Strategies"
            />
            <span className="tooltip">Strategies</span>
          </li>

          <li
            onClick={() => {
              navigate("/Equity");
              deleteEmptyRow();
            }}
          >
            <img
              style={{
                height: "50px",
                width: "50px",
                padding: " 8px 10px ",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                transition: "background-color 0.3s",
                backgroundColor: pathname === "/Equity" ? "#4661bd" : "#d8e1ff",
                cursor: "pointer",
              }}
              src={leftSideBar3}
              alt="Equity Trading"
            />
            <span className="tooltip">Equity Trading</span>
          </li>

          <li
            onClick={() => {
              navigate("/F&O/Portfolio");
              deleteEmptyRow();
            }}
          >
            <img
              style={{
                height: "50px",
                width: "50px",
                padding: "6px",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                transition: "background-color 0.3s",
                backgroundColor: pathname === "/F&O/Portfolio" ? "#4661bd" : "#d8e1ff",
                cursor: "pointer",
              }}
              src={leftSideBar4}
              alt="F&O Trading"
            />
            <span className="tooltip">F&O Trading</span>
          </li>

          <li
            onClick={() => {
              navigate("/Master_accounts");
              deleteEmptyRow();
            }}
          >
            <img
              style={{
                height: "50px",
                width: "50px",
                padding: "6px",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                transition: "background-color 0.3s",
                backgroundColor: pathname === "/Master_accounts" ? "#4661bd" : "#d8e1ff",
                cursor: "pointer",
              }}
              src={leftSideBar5}
              alt="Master Child"
            />
            <span className="tooltip">Master Child</span>
          </li>
          <li
            onClick={() => {
              navigate("/TradingView");
              deleteEmptyRow();
            }}
          >
            <img
              style={{
                height: "50px",
                width: "50px",
                padding: "6px",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                transition: "background-color 0.3s",
                backgroundColor: pathname === "/TradingView" ? "#4661bd" : "#d8e1ff",
                cursor: "pointer",
              }}
              src={rightBarTools}
              alt="TradingView"
            />
            <span className="tooltip">TradingView</span>
          </li>
        </ul>
      </div>

      <span className="chat-icon-button" style={{
        display: "flex",
        justifyContent: "center",
        padding: "10px",
      }}>
        <ChatBubbleOutlineIcon style={{ fontSize: "24px", cursor: "pointer", color: "#4661bd" }} />
        <span className="tooltip">Chat</span>
      </span>
    </div>
  );
}

export default LeftNav;